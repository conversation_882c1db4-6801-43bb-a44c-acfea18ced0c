/* Reset and Base Styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "Satoshi", sans-serif;
	/* background-color: #111111; */
	background-color: #000000;
	color: #ffffff;
	line-height: 1.6;
	overflow-x: hidden;
	max-width: 1440px;
	margin: 0 auto;
}

/* Navigation */
.navbar {
	max-width: 1440px;
	position: fixed;
	top: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10px);
	z-index: 1000;
	padding: 16px 0;
}

.nav-container {
	margin: 0 auto;
	padding: 0 120px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 40px;
}

.logo-text {
	color: #ffffff;
	font-size: 20px;
	font-weight: 600;
}

.nav-menu {
	display: flex;
	gap: 40px;
	align-items: center;
}

.nav-link {
	color: #ffffff;
	text-decoration: none;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	transition: color 0.3s ease;
}

.nav-link:hover {
	color: #dc2626;
}

.nav-cta {
	display: flex;
	align-items: center;
}

.join-presale-btn {
	background: transparent;
	color: #ffffff;
	border: 1px solid #ffffff50;
	padding: 9px 16px;
	font-size: 14px;
	line-height: 22px;
	font-weight: 400;
	border-radius: 10000px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.join-presale-btn:hover {
	background: #ffffff;
	color: #000000;
}

/* Hero Section */
.hero {
	display: flex;
	align-items: center;
	background-image: url("assets/backgrounds/bg.png");
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
	overflow: hidden;
	aspect-ratio: 8/5;
	width: 100%;
	max-width: 1440px;
}

.hero-container {
	padding: 0 120px;
	display: flex;
	align-items: center;
	height: 100%;
	width: 100%;
}

.hero-content {
	max-width: 650px;
	z-index: 2;
}

.hero-title {
	font-size: 88px;
	font-weight: 700;
	line-height: 102px;
	margin-bottom: 32px;
	color: #ffffff;
}

.hero-description {
	font-size: 20px;
	font-weight: 400;
	line-height: 36px;
	margin-bottom: 40px;
	color: #ffffff;
	opacity: 0.9;
}

.hero-buttons {
	display: flex;
	gap: 16px;
	align-items: center;
}

.join-presale-button {
	background: linear-gradient(180deg, #f2133c 0%, #dc251d 100%);
	border: 1px solid #f2133c;
	color: #ffffff;
	padding: 0 24.5px;
	font-size: 18px;
	line-height: 26px;
	font-weight: 500;
	border-radius: 10000px;
	cursor: pointer;
	transition: all 0.3s ease;
	height: 56px;
}

.join-presale-button:hover {
	background: linear-gradient(180deg, #f2133c 0%, #e63939 100%);
	transform: translateY(-2px);
}

.learn-more-button {
	background: transparent;
	color: #ffffff;
	border: 1px solid #ffffff;
	padding: 0 27.5px;
	font-size: 18px;
	line-height: 26px;
	font-weight: 500;
	border-radius: 10000px;
	cursor: pointer;
	transition: all 0.3s ease;
	height: 56px;
}

.learn-more-button:hover {
	background: #ffffff;
	color: #000000;
	transform: translateY(-2px);
}

/* Statistics Section */
.stats {
	padding: 80px 0;
}

.stats-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 120px;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 120px;
	text-align: left;
}

.stat-item {
	position: relative;
}

.stat-item:not(:last-child)::after {
	content: "";
	position: absolute;
	right: -60px;
	top: 50%;
	transform: translateY(-50%);
	width: 1px;
	height: 200px;
	background: linear-gradient(
		180deg,
		rgba(255, 255, 255, 0) 0%,
		rgba(255, 255, 255, 0.12) 25%,
		rgba(255, 255, 255, 0.12) 50%,
		rgba(255, 255, 255, 0.12) 75%,
		rgba(255, 255, 255, 0) 100%
	);
}

.stat-number {
	font-size: 56px;
	font-weight: 400;
	color: #ffffff;
	margin-bottom: 30px;
	line-height: 64px;
	position: relative;
}

.stat-number::before {
	content: "";
	position: absolute;
	bottom: -15px;
	width: 40px;
	height: 3px;
	background: #dc2626;
}

.stat-label {
	font-size: 16px;
	color: #ffffff;
	font-weight: 400;
	line-height: 24px;
}

/* Responsive Design */
@media (max-width: 1440px) {
	body {
		max-width: 100%;
	}
}

@media (max-width: 1200px) {
	.nav-container,
	.hero-container,
	.stats-container,
	.digitizing-container {
		padding: 0 40px;
	}

	.stats-container {
		gap: 80px;
	}

	.digitizing-container {
		gap: 60px;
	}

	.digitizing-title {
		font-size: 40px;
	}
}

@media (max-width: 768px) {
	.nav-container {
		flex-direction: column;
		gap: 20px;
	}

	.nav-menu {
		gap: 20px;
	}

	.hero {
		aspect-ratio: 4/3;
		min-height: 500px;
	}

	.hero-buttons {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.join-presale-button,
	.learn-more-button {
		width: 100%;
		text-align: center;
	}

	.stats-container {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.stat-item:not(:last-child)::after {
		display: none;
	}

	.hero-title {
		font-size: 48px;
	}

	.digitizing-container {
		grid-template-columns: 1fr;
		gap: 40px;
		text-align: center;
	}

	.digitizing-title {
		font-size: 36px;
	}

	.digitizing {
		padding: 80px 0;
	}
}

/* Digitizing Section */
.digitizing {
}

.digitizing-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 120px;
	display: flex;
	gap: 80px;
	align-items: center;
}

.digitizing-content {
	color: #ffffff;
	min-width: 44%;
}

.digitizing-title {
	font-family: "Satoshi", sans-serif;
	font-size: 48px;
	font-weight: 700;
	line-height: 1.2;
	margin-bottom: 24px;
	color: #ffffff;
}

.digitizing-description {
	font-size: 16px;
	font-weight: 400;
	line-height: 1.6;
	color: #cccccc;
	margin-bottom: 32px;
}

.digitizing-features {
	margin-bottom: 40px;
}

.digitizing-feature {
	font-size: 16px;
	font-weight: 400;
	color: #ffffff;
	margin-bottom: 16px;
}

.feature-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 12px;
}

.feature-icon {
	color: #ff4444;
	font-size: 16px;
	font-weight: 600;
}

.feature-text {
	font-size: 16px;
	font-weight: 400;
	color: #cccccc;
	line-height: 1.5;
}

.explore-horse-btn {
	background: linear-gradient(180deg, #f2133c 0%, #dc251d 100%);
	border: 1px solid #f2133c;
	color: #ffffff;
	padding: 0 24.5px;
	font-size: 18px;
	line-height: 26px;
	font-weight: 500;
	border-radius: 10000px;
	cursor: pointer;
	transition: all 0.3s ease;
	height: 56px;
}

.explore-horse-btn:hover {
	background: linear-gradient(180deg, #f2133c 0%, #e63939 100%);
	transform: translateY(-2px);
}

.digitizing-image {
	display: flex;
	justify-content: center;
	align-items: center;
}

.digitizing-image img {
	/* max-width: 100%; */
	max-width: 800px;
	height: auto;
	border-radius: 12px;
}
