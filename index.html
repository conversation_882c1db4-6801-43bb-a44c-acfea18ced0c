<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>EquineFi - The Home of EquineFi</title>
		<link rel="stylesheet" href="styles.css" />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
			rel="stylesheet"
		/>
		<link href="https://fonts.cdnfonts.com/css/satoshi" rel="stylesheet" />
	</head>
	<body>
		<!-- Navigation -->
		<nav class="navbar">
			<div class="nav-container">
				<div class="nav-logo">
					<span class="logo-text">Logo</span>
				</div>
				<div class="nav-menu">
					<a href="#" class="nav-link">About</a>
					<a href="#" class="nav-link">$HORSE</a>
					<a href="#" class="nav-link">Marketplace</a>
					<a href="#" class="nav-link">Partners</a>
					<a href="#" class="nav-link">Contacts</a>
				</div>
				<div class="nav-cta">
					<button class="join-presale-btn">Join Presale</button>
				</div>
			</div>
		</nav>

		<!-- Hero Section -->
		<section class="hero">
			<div class="hero-container">
				<div class="hero-content">
					<h1 class="hero-title">
						The Home of
						<br />
						EquineFi
					</h1>
					<p class="hero-description">
						Own racehorses via NFTs, trade on the marketplace, earn rewards, stake $HORSE, bet on-chain and
						access real-time equine data — powered by Web3 and Secretariat's legacy.
					</p>
					<div class="hero-buttons">
						<button class="join-presale-button">Join Presale</button>
						<button class="learn-more-button">Learn More</button>
					</div>
				</div>
			</div>
		</section>

		<!-- Statistics Section -->
		<section class="stats">
			<div class="stats-container">
				<div class="stat-item">
					<h3 class="stat-number">$177B</h3>
					<p class="stat-label">Equine Industry</p>
				</div>
				<div class="stat-item">
					<h3 class="stat-number">250k+</h3>
					<p class="stat-label">Users by 2029</p>
				</div>
				<div class="stat-item">
					<h3 class="stat-number">$10M-$14M</h3>
					<p class="stat-label">Year 1 Revenue</p>
				</div>
			</div>
		</section>

		<!-- Digitizing Section -->
		<section class="digitizing">
			<div class="digitizing-container">
				<div class="digitizing-content">
					<h2 class="digitizing-title">
						Digitizing the
						<br />
						Equine Industry
					</h2>
					<p class="digitizing-description">
						Big Red ($HORSE) brings the equine industry on-chain, solving real problems: delayed payouts,
						paperwork and opaque data.
					</p>
					<div class="digitizing-features">
						<p class="digitizing-feature">We offer:</p>
						<div class="feature-list">
							<div class="feature-item">
								<span class="feature-icon">✓</span>
								<span class="feature-text">NFT-based horse and stable ownership.</span>
							</div>
							<div class="feature-item">
								<span class="feature-icon">✓</span>
								<span class="feature-text">Weekly race earnings on-chain.</span>
							</div>
							<div class="feature-item">
								<span class="feature-icon">✓</span>
								<span class="feature-text">Transparent data: jockeys, lineage, vet stats.</span>
							</div>
						</div>
					</div>
					<button class="explore-horse-btn">Explore $HORSE</button>
				</div>
				<div class="digitizing-image">
					<img src="assets/images/digitizing.png" alt="Digitizing the Equine Industry" />
				</div>
			</div>
		</section>

		<!-- Big Red Ecosystem Section -->
		<section class="ecosystem">
			<div class="ecosystem-container">
				<h2 class="ecosystem-title">
					The
					<span class="ecosystem-title-red">Big Red</span>
					Ecosystem
				</h2>
				<div class="ecosystem-grid">
					<div class="ecosystem-item">
						<div class="ecosystem-icon">
							<img src="assets/icons/horse-stables.svg" alt="RWA Horse Stables" />
						</div>
						<h3 class="ecosystem-item-title">RWA Horse Stables</h3>
						<p class="ecosystem-item-description">
							Own racehorses via NFTs across three lines: Harness, Thoroughbred and Breeding. Currently 5
							horses on stable, scaling to 200+.
						</p>
					</div>
					<div class="ecosystem-item">
						<div class="ecosystem-icon">
							<img src="assets/icons/nft-marketplace.svg" alt="NFT Marketplace" />
						</div>
						<h3 class="ecosystem-item-title">NFT Marketplace</h3>
						<p class="ecosystem-item-description">
							Trade and rent racehorse shares, breeding rights, and collectibles — interoperable across
							betting and virtual games.
						</p>
					</div>
					<div class="ecosystem-item">
						<div class="ecosystem-icon">
							<img src="assets/icons/betting-engine.svg" alt="Syndicate Betting Engine" />
						</div>
						<h3 class="ecosystem-item-title">Syndicate Betting Engine (Launch 2025)</h3>
						<p class="ecosystem-item-description">
							Join pooled betting using $HORSE. Decentralized, fair and shared winnings via smart
							contracts.
						</p>
					</div>
					<div class="ecosystem-item">
						<div class="ecosystem-icon">
							<img src="assets/icons/virtual-racing.svg" alt="Virtual Racing Game" />
						</div>
						<h3 class="ecosystem-item-title">Virtual Racing Game (Launch 2026)</h3>
						<p class="ecosystem-item-description">
							Race your NFTs in simulated events. Bet, win and compete 24/7 using verifiable data.
						</p>
					</div>
					<div class="ecosystem-item">
						<div class="ecosystem-icon">
							<img src="assets/icons/equine-data.svg" alt="Tokenized Equine Data" />
						</div>
						<h3 class="ecosystem-item-title">Tokenized Equine Data</h3>
						<p class="ecosystem-item-description">
							Vet records, race stats, breeding lineage — all on-chain and accessible via oracles or
							partner APIs.
						</p>
					</div>
				</div>
			</div>
		</section>

		<!-- $HORSE Power Section -->
		<section class="horse-power">
			<div class="horse-power-container">
				<div class="horse-power-content">
					<div class="horse-power-left">
						<h2 class="horse-power-title">
							<span class="horse-power-title-red">$HORSE</span>
							Power
							<br />
							in action
						</h2>
						<p class="horse-power-subtitle">$HORSE: The Utility Token of the Equine Economy</p>
						<p class="horse-power-description">
							A deflationary token with real-world backing, embedded utility and structured rewards across
							an expanding equine ecosystem.
						</p>
						<div class="horse-power-buttons">
							<button class="join-presale-button">Join Presale</button>
							<button class="stake-tiers-button">Stake Tiers</button>
						</div>
					</div>
					<div class="horse-power-right">
						<h3 class="key-uses-title">Key Uses:</h3>
						<div class="key-uses-list">
							<div class="key-use-item">
								<span class="key-use-icon">✓</span>
								<span class="key-use-text">Buy NFTs: Horses, stables, breeding rights.</span>
							</div>
							<div class="key-use-item">
								<span class="key-use-icon">✓</span>
								<span class="key-use-text">Stake: Up to 20% APY, revenue share, governance.</span>
							</div>
							<div class="key-use-item">
								<span class="key-use-icon">✓</span>
								<span class="key-use-text">Bet: On-chain syndicates & virtual races.</span>
							</div>
							<div class="key-use-item">
								<span class="key-use-icon">✓</span>
								<span class="key-use-text">Access Data: Unlock premium stats and records.</span>
							</div>
							<div class="key-use-item">
								<span class="key-use-icon">✓</span>
								<span class="key-use-text">Deflation: 27%+ supply burned or bought back by 2029.</span>
							</div>
						</div>
						<p class="horse-power-footer">
							$HORSE isn't built for speculation — it's built for circulation. It powers ownership,
							betting, rewards and access across the entire Big Red platform.
						</p>
					</div>
				</div>
			</div>
			<div class="horse-power-background">
				<div class="masked-container">
					<svg viewBox="0 0 1200 600" preserveAspectRatio="xMidYMid slice">
						<defs>
							<mask id="custom-mask">
								<!-- Entire area black (hidden) -->
								<rect width="100%" height="100%" fill="black" />
								<!-- Add visible areas as white shapes -->
								<rect x="100" y="100" width="1000" height="400" fill="white" rx="10" />
								<polygon points="0,0 80,0 0,80" fill="white" />
								<polygon points="1120,600 1200,600 1200,520" fill="white" />
							</mask>
						</defs>
						<image href="assets/images/horse.png" width="1200" height="600" mask="url(#custom-mask)" />
					</svg>
				</div>
			</div>
		</section>
	</body>
</html>
